name: db_query_pre_auth
author: junjiem
icon: icon.svg
created_at: "2025-01-13T09:55:01.5573645+08:00"
label:
  en_US: Database Query (Pre-authorization)
  zh_Hans: 数据库查询（预授权）
description:
  en_US: Database Query Utils (Pre-authorization).
  zh_Hans: 数据库查询工具（预授权）。
meta:
  arch:
    - amd64
    - arm64
  runner:
    entrypoint: main
    language: python
    version: "3.12"
  version: 0.0.1
plugins:
  tools:
    - provider/db_query.yaml
resource:
  memory: 1048576
  permission:
    model:
      enabled: true
      llm: true
    tool:
      enabled: true
tags:
  - search
  - utilities
type: plugin
version: 0.0.8
privacy: PRIVACY.md
verified: false