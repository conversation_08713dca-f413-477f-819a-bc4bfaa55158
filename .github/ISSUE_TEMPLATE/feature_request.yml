name: "⭐ Feature or enhancement request 特性或增强请求"
description: Propose something new. 提出一些新的建议。
labels:
  - enhancement
body:
  - type: checkboxes
    attributes:
      label: "Please confirm before submission 在提交之前，请确认"
      options:
        - label: |
            I have searched for existing issues [search for existing issues](https://github.com/junjiem/dify-plugin-tools-dbquery/issues), including closed ones. 
            我已经搜索了现有问题[搜索现有问题](https://github.com/junjiem/dify-plugin-tools-dbquery/issues)，包括已关闭的问题。"
          required: true
  - type: textarea
    attributes:
      label: Is this request related to a challenge you're experiencing? Tell me about your story. 这个请求与你正在经历的挑战有关吗？给我讲讲你的故事。
      placeholder: |
        Please describe the specific scenario or problem you're facing as clearly as possible. For instance "I was trying to use [feature] for [specific task], and [what happened]... It was frustrating because...."
        请尽可能清楚地描述你所面临的具体场景或问题。例如：“我试图将[功能]用于[特定任务]，但[发生了什么]……这很令人沮丧，因为....”
    validations:
      required: true
  - type: checkboxes
    attributes:
      label: Can you help us with this feature? 你能帮我们实现这个功能吗？
      description: Let us know! This is not a commitment, but a starting point for collaboration. 让我们知道！这不是承诺，而是合作的起点。
      options:
        - label: I am interested in contributing to this feature. 我有兴趣为这个特性做贡献。
          required: false
  - type: markdown
    attributes:
      value: Please limit one request per issue. 请限制每个问题一个请求。
